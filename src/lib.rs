extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    collections::{HashMap, HashSet},
    error::Error,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{<PERSON><PERSON><PERSON><PERSON><PERSON>, JsonStreamReader, ReaderSettings};

// Global thread-local variable for column index mapping during projection pushdown
thread_local! {
    static COLUMN_MAPPING: std::cell::RefCell<Option<Vec<usize>>> = std::cell::RefCell::new(None);
}

/// Create a JsonStreamReader with settings optimized for DuckDB JSON extension
fn create_json_reader<R: std::io::Read>(reader: R) -> JsonStreamReader<R> {
    let settings = ReaderSettings {
        // Disable number value restrictions to handle large/small numbers like 1.7976931348623157e+308
        restrict_number_values: false,
        // Keep other defaults
        ..Default::default()
    };
    JsonStreamReader::new_custom(reader, settings)
}



/// Represents the complete inferred schema with statistics
#[derive(Debug, Clone)]
pub struct InferredSchema {
    pub root_type: InferredJsonType,
    pub statistics: SchemaStatistics,
    pub memory_requirements: MemoryRequirements,
}

/// Recursive JSON type representation with capacity information for exact vector allocation
#[derive(Debug, Clone)]
pub enum InferredJsonType {
    Null,
    Boolean,
    Number,
    String {
        max_length: Option<usize>,
        total_instances: usize,
    },
    Array {
        element_type: Box<InferredJsonType>,
        max_length: Option<usize>,
        total_elements: usize,
        array_count: usize,
    },
    Object {
        fields: Vec<(String, InferredJsonType)>, // Preserve field order from JSON
        total_instances: usize,
    },
}

/// Schema statistics for memory planning and validation
#[derive(Debug, Clone)]
pub struct SchemaStatistics {
    pub total_rows: usize,
    pub max_nesting_depth: usize,
    pub unique_field_names: HashSet<String>,
    pub schema_complexity_score: usize,
}

/// Exact memory requirements calculated from schema
#[derive(Debug, Clone)]
pub struct MemoryRequirements {
    pub vector_capacities: HashMap<VectorPath, usize>,
    pub total_memory_estimate: usize,
    pub peak_memory_estimate: usize,
}

/// Identifies specific vectors in the nested structure
#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub struct VectorPath {
    pub path_components: Vec<PathComponent>,
}

#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub enum PathComponent {
    Root,
    ArrayElement,
    ObjectField(String),
}

/// Configuration for schema inference behavior
#[derive(Debug, Clone)]
pub struct SchemaInferenceConfig {
    pub max_memory_usage: Option<usize>,
    pub max_schema_complexity: usize,
    pub max_unique_fields: usize,
    pub enable_progress_reporting: bool,
    pub enable_debug_output: bool,
}

/// Memory and performance limits
const MAX_SCHEMA_COMPLEXITY: usize = 10_000_000;
const MAX_UNIQUE_FIELD_NAMES: usize = 10_000;
const MAX_ARRAY_ELEMENTS_FOR_INFERENCE: usize = 1000;
const DEFAULT_MEMORY_LIMIT: usize = 100 * 1024 * 1024;

impl Default for SchemaInferenceConfig {
    fn default() -> Self {
        Self {
            max_memory_usage: Some(DEFAULT_MEMORY_LIMIT),
            max_schema_complexity: MAX_SCHEMA_COMPLEXITY,
            max_unique_fields: MAX_UNIQUE_FIELD_NAMES,
            enable_progress_reporting: false,
            enable_debug_output: false,
        }
    }
}

/// Comprehensive error types for schema inference
#[derive(Debug)]
pub enum SchemaInferenceError {
    FileError(std::io::Error),
    JsonError { position: usize, message: String },
    ComplexityExceeded(usize),
    TooManyFields(usize),
    MemoryError(String),
    TypeMergeConflict { type1: String, type2: String },
}

impl std::fmt::Display for SchemaInferenceError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SchemaInferenceError::FileError(e) => write!(f, "File I/O error: {}", e),
            SchemaInferenceError::JsonError { position, message } => {
                write!(f, "JSON parsing error at position {}: {}", position, message)
            },
            SchemaInferenceError::ComplexityExceeded(complexity) => {
                write!(f, "Schema complexity exceeded limit: {} > {}", complexity, MAX_SCHEMA_COMPLEXITY)
            },
            SchemaInferenceError::TooManyFields(count) => {
                write!(f, "Too many unique field names: {} > {}", count, MAX_UNIQUE_FIELD_NAMES)
            },
            SchemaInferenceError::MemoryError(msg) => write!(f, "Memory allocation failed: {}", msg),
            SchemaInferenceError::TypeMergeConflict { type1, type2 } => {
                write!(f, "Type merging conflict: cannot merge {} with {}", type1, type2)
            },
        }
    }
}

impl std::error::Error for SchemaInferenceError {}

impl From<std::io::Error> for SchemaInferenceError {
    fn from(error: std::io::Error) -> Self {
        SchemaInferenceError::FileError(error)
    }
}

impl From<String> for SchemaInferenceError {
    fn from(error: String) -> Self {
        SchemaInferenceError::MemoryError(error)
    }
}

/// Core schema inference engine using pure struson streaming
pub struct SchemaInferrer {
    current_schema: Option<InferredJsonType>,
    nesting_stack: Vec<InferenceContext>,
    statistics: SchemaStatistics,
    memory_tracker: MemoryUsageTracker,
    config: SchemaInferenceConfig,
}

/// Context for tracking inference state at each nesting level
#[derive(Debug)]
struct InferenceContext {
    context_type: ContextType,
    field_schemas: HashMap<String, InferredJsonType>,
    element_count: usize,
    max_array_length: usize,
}

#[derive(Debug)]
enum ContextType {
    RootLevel,
    ArrayElement,
    ObjectField(String),
}

/// Memory usage tracking and bounds enforcement
pub struct MemoryUsageTracker {
    baseline_memory: usize,
    peak_memory: usize,
    current_memory: usize,
    memory_limit: Option<usize>,
    checkpoints: Vec<MemoryCheckpoint>,
}

#[derive(Debug, Clone)]
struct MemoryCheckpoint {
    name: String,
    memory_usage: usize,
    timestamp: std::time::Instant,
}

impl MemoryUsageTracker {
    pub fn new(memory_limit: Option<usize>) -> Self {
        Self {
            baseline_memory: 0,
            peak_memory: 0,
            current_memory: 0,
            memory_limit,
            checkpoints: Vec::new(),
        }
    }

    pub fn start_monitoring(&mut self) {
        self.baseline_memory = self.get_current_memory_usage();
        self.current_memory = self.baseline_memory;
        self.peak_memory = self.baseline_memory;
        self.record_checkpoint("baseline");
    }

    pub fn check_memory_bounds(&mut self) -> Result<(), SchemaInferenceError> {
        self.current_memory = self.get_current_memory_usage();
        self.peak_memory = self.peak_memory.max(self.current_memory);

        if let Some(limit) = self.memory_limit {
            if self.current_memory > limit {
                return Err(SchemaInferenceError::MemoryError(
                    format!("Memory usage {} exceeds limit {}", self.current_memory, limit)
                ));
            }
        }

        Ok(())
    }

    pub fn record_checkpoint(&mut self, name: &str) {
        self.current_memory = self.get_current_memory_usage();
        self.checkpoints.push(MemoryCheckpoint {
            name: name.to_string(),
            memory_usage: self.current_memory,
            timestamp: std::time::Instant::now(),
        });
    }

    fn get_current_memory_usage(&self) -> usize {
        // Platform-specific memory usage detection
        #[cfg(target_os = "linux")]
        {
            self.get_linux_memory_usage()
        }
        #[cfg(not(target_os = "linux"))]
        {
            // Estimate based on heap allocations
            0 // Placeholder - would need platform-specific implementation
        }
    }

    #[cfg(target_os = "linux")]
    fn get_linux_memory_usage(&self) -> usize {
        use std::fs;
        if let Ok(status) = fs::read_to_string("/proc/self/status") {
            for line in status.lines() {
                if line.starts_with("VmRSS:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = kb_str.parse::<usize>() {
                            return kb * 1024; // Convert KB to bytes
                        }
                    }
                }
            }
        }
        0
    }

    pub fn get_memory_report(&self) -> MemoryReport {
        MemoryReport {
            baseline_memory: self.baseline_memory,
            peak_memory: self.peak_memory,
            current_memory: self.current_memory,
            memory_delta: self.current_memory.saturating_sub(self.baseline_memory),
            checkpoints: self.checkpoints.clone(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct MemoryReport {
    pub baseline_memory: usize,
    pub peak_memory: usize,
    pub current_memory: usize,
    pub memory_delta: usize,
    pub checkpoints: Vec<MemoryCheckpoint>,
}

impl SchemaInferrer {
    pub fn new(config: SchemaInferenceConfig) -> Self {
        Self {
            current_schema: None,
            nesting_stack: Vec::new(),
            statistics: SchemaStatistics {
                total_rows: 0,
                max_nesting_depth: 0,
                unique_field_names: HashSet::new(),
                schema_complexity_score: 0,
            },
            memory_tracker: MemoryUsageTracker::new(config.max_memory_usage),
            config,
        }
    }

    /// Infer schema from streaming JSON reader
    pub fn infer_from_stream(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        self.memory_tracker.start_monitoring();

        let inferred_type = self.infer_value_recursive(json_reader)?;

        // Validate memory bounds
        let complexity = inferred_type.calculate_complexity();
        if complexity > self.config.max_schema_complexity {
            return Err(SchemaInferenceError::ComplexityExceeded(complexity));
        }

        self.statistics.schema_complexity_score = complexity;

        Ok(inferred_type)
    }

    /// Recursive schema inference for arbitrary nesting depth
    fn infer_value_recursive(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        // Check memory bounds periodically
        self.memory_tracker.check_memory_bounds()?;

        // Track nesting depth
        let current_depth = self.nesting_stack.len();
        self.statistics.max_nesting_depth = self.statistics.max_nesting_depth.max(current_depth);

        match json_reader.peek().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            struson::reader::ValueType::Null => {
                json_reader.next_null().map_err(|e| SchemaInferenceError::JsonError {
                    position: 0,
                    message: e.to_string()
                })?;
                Ok(InferredJsonType::Null)
            },

            struson::reader::ValueType::Boolean => {
                json_reader.next_bool().map_err(|e| SchemaInferenceError::JsonError {
                    position: 0,
                    message: e.to_string()
                })?;
                Ok(InferredJsonType::Boolean)
            },

            struson::reader::ValueType::Number => {
                json_reader.next_number_as_str().map_err(|e| SchemaInferenceError::JsonError {
                    position: 0,
                    message: e.to_string()
                })?;
                Ok(InferredJsonType::Number)
            },

            struson::reader::ValueType::String => {
                let string_value = json_reader.next_string().map_err(|e| SchemaInferenceError::JsonError {
                    position: 0,
                    message: e.to_string()
                })?;
                Ok(InferredJsonType::String {
                    max_length: Some(string_value.len()),
                    total_instances: 1,
                })
            },

            struson::reader::ValueType::Array => {
                self.infer_array_schema(json_reader)
            },

            struson::reader::ValueType::Object => {
                self.infer_object_schema(json_reader)
            },
        }
    }

    /// Infer schema for JSON arrays with element type analysis
    fn infer_array_schema(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        json_reader.begin_array().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        let mut element_type: Option<InferredJsonType> = None;
        let mut element_count = 0;
        let mut total_elements = 0;

        // Push array context
        self.nesting_stack.push(InferenceContext {
            context_type: ContextType::ArrayElement,
            field_schemas: HashMap::new(),
            element_count: 0,
            max_array_length: 0,
        });

        while json_reader.has_next().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            let current_element = self.infer_value_recursive(json_reader)?;
            element_count += 1;
            total_elements += current_element.count_total_elements();

            element_type = Some(match element_type {
                None => current_element,
                Some(existing) => self.merge_types(existing, current_element)?,
            });

            // Check memory bounds during inference
            if element_count > MAX_ARRAY_ELEMENTS_FOR_INFERENCE {
                break; // Stop inferring after reasonable sample
            }
        }

        json_reader.end_array().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        // Pop array context and update statistics
        if let Some(mut context) = self.nesting_stack.pop() {
            context.element_count = element_count;
            context.max_array_length = element_count;
        }

        Ok(InferredJsonType::Array {
            element_type: Box::new(element_type.unwrap_or(InferredJsonType::Null)),
            max_length: Some(element_count),
            total_elements,
            array_count: 1,
        })
    }

    /// Infer schema for JSON objects with field analysis
    fn infer_object_schema(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        json_reader.begin_object().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        let mut fields = Vec::new();
        let mut field_count = 0;

        // Push object context
        self.nesting_stack.push(InferenceContext {
            context_type: ContextType::RootLevel,
            field_schemas: HashMap::new(),
            element_count: 0,
            max_array_length: 0,
        });

        while json_reader.has_next().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            let field_name = json_reader.next_name().map_err(|e| SchemaInferenceError::JsonError {
                position: 0,
                message: e.to_string()
            })?.to_string();
            let field_type = self.infer_value_recursive(json_reader)?;

            fields.push((field_name.clone(), field_type));
            field_count += 1;

            // Track unique field names for memory bounds
            self.statistics.unique_field_names.insert(field_name);

            // Check field count bounds
            if self.statistics.unique_field_names.len() > self.config.max_unique_fields {
                return Err(SchemaInferenceError::TooManyFields(
                    self.statistics.unique_field_names.len()
                ));
            }
        }

        json_reader.end_object().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        // Pop object context
        self.nesting_stack.pop();

        Ok(InferredJsonType::Object {
            fields,
            total_instances: 1,
        })
    }

    /// Merge two JSON types to handle heterogeneous arrays/objects
    fn merge_types(
        &self,
        type1: InferredJsonType,
        type2: InferredJsonType
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        match (type1, type2) {
            // Same types: merge statistics
            (InferredJsonType::String { max_length: len1, total_instances: count1 },
             InferredJsonType::String { max_length: len2, total_instances: count2 }) => {
                Ok(InferredJsonType::String {
                    max_length: Some(len1.unwrap_or(0).max(len2.unwrap_or(0))),
                    total_instances: count1 + count2,
                })
            },

            // Number types: merge (all numbers are compatible)
            (InferredJsonType::Number, InferredJsonType::Number) => {
                Ok(InferredJsonType::Number)
            },

            // Boolean types: merge (all booleans are compatible)
            (InferredJsonType::Boolean, InferredJsonType::Boolean) => {
                Ok(InferredJsonType::Boolean)
            },

            // Array types: merge element types and statistics
            (InferredJsonType::Array { element_type: elem1, total_elements: total1, array_count: count1, .. },
             InferredJsonType::Array { element_type: elem2, total_elements: total2, array_count: count2, .. }) => {
                let merged_element = self.merge_types(*elem1, *elem2)?;
                Ok(InferredJsonType::Array {
                    element_type: Box::new(merged_element),
                    max_length: None, // Will be calculated during capacity planning
                    // total_elements should be the max elements per array, not cumulative
                    total_elements: total1.max(total2),
                    array_count: count1 + count2,
                })
            },

            // Object types: merge field schemas
            (InferredJsonType::Object { fields: fields1, total_instances: count1, .. },
             InferredJsonType::Object { fields: fields2, total_instances: count2, .. }) => {
                let mut merged_fields = fields1;

                for (field_name, field_type2) in fields2 {
                    // Find existing field by name
                    if let Some(pos) = merged_fields.iter().position(|(name, _)| name == &field_name) {
                        // Merge existing field
                        let (_, field_type1) = &merged_fields[pos];
                        let merged_field = self.merge_types(field_type1.clone(), field_type2)?;
                        merged_fields[pos] = (field_name, merged_field);
                    } else {
                        // Add new field (preserves order)
                        merged_fields.push((field_name, field_type2));
                    }
                }

                Ok(InferredJsonType::Object {
                    fields: merged_fields,
                    total_instances: count1 + count2,
                })
            },

            // Null can merge with any type (becomes optional)
            (InferredJsonType::Null, other) | (other, InferredJsonType::Null) => {
                Ok(other) // Null doesn't change the type, just makes it optional
            },

            // Incompatible types: error
            (t1, t2) => Err(SchemaInferenceError::TypeMergeConflict {
                type1: format!("{:?}", t1),
                type2: format!("{:?}", t2)
            }),
        }
    }
}

impl InferredJsonType {


    /// Get the memory footprint of this schema representation
    pub fn memory_footprint(&self) -> usize {
        match self {
            InferredJsonType::Null |
            InferredJsonType::Boolean |
            InferredJsonType::Number => std::mem::size_of::<Self>(),

            InferredJsonType::String { .. } => {
                std::mem::size_of::<Self>() + std::mem::size_of::<usize>() * 2
            },

            InferredJsonType::Array { element_type, .. } => {
                std::mem::size_of::<Self>() + element_type.memory_footprint()
            },

            InferredJsonType::Object { fields, .. } => {
                let fields_size = fields.iter()
                    .map(|(name, field_type)| name.len() + field_type.memory_footprint())
                    .sum::<usize>();
                std::mem::size_of::<Self>() + fields_size
            },
        }
    }

    /// Optimize schema representation by removing redundant information
    pub fn optimize(&mut self) {
        match self {
            InferredJsonType::Array { element_type, .. } => {
                element_type.optimize();
            },
            InferredJsonType::Object { fields, .. } => {
                for (_, field_type) in fields.iter_mut() {
                    field_type.optimize();
                }
                // Remove fields with zero instances (if any)
                fields.retain(|(_, field_type)| field_type.count_total_elements() > 0);
            },
            _ => {}, // Primitives don't need optimization
        }
    }

    /// Convert InferredJsonType to DuckDB LogicalTypeHandle
    pub fn to_duckdb_type(&self) -> Result<LogicalTypeHandle, Box<dyn std::error::Error>> {
        match self {
            InferredJsonType::Null => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)), // Null becomes nullable VARCHAR
            InferredJsonType::Boolean => Ok(LogicalTypeHandle::from(LogicalTypeId::Boolean)),
            InferredJsonType::Number => Ok(LogicalTypeHandle::from(LogicalTypeId::Double)),
            InferredJsonType::String { .. } => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),

            InferredJsonType::Array { element_type, .. } => {
                // Create proper LIST type with correct element type
                let element_logical_type = element_type.to_duckdb_type()?;
                Ok(LogicalTypeHandle::list(&element_logical_type))
            },

            InferredJsonType::Object { fields, .. } => {
                // Create proper STRUCT type for nested objects
                let mut struct_fields = Vec::new();

                for (field_name, field_type) in fields {
                    let field_logical_type = field_type.to_duckdb_type()?;
                    struct_fields.push((field_name.as_str(), field_logical_type));
                }

                Ok(LogicalTypeHandle::struct_type(&struct_fields))
            },
        }
    }

    /// Calculate memory complexity score for this type
    pub fn calculate_complexity(&self) -> usize {
        match self {
            InferredJsonType::Null |
            InferredJsonType::Boolean |
            InferredJsonType::Number => 1,

            InferredJsonType::String { max_length, .. } => {
                // String complexity based on maximum length
                max_length.unwrap_or(100).min(1000) // Cap at reasonable limit
            },

            InferredJsonType::Array { element_type, array_count, .. } => {
                // Array complexity: base cost + element complexity * array count
                10 + element_type.calculate_complexity() * (*array_count).min(1000)
            },

            InferredJsonType::Object { fields, total_instances, .. } => {
                // Object complexity: field count + sum of field complexities
                let field_complexity: usize = fields.iter()
                    .map(|(_, field_type)| field_type.calculate_complexity())
                    .sum();
                fields.len() * 5 + field_complexity * (*total_instances).min(1000)
            },
        }
    }

    /// Count total elements for capacity calculation
    pub fn count_total_elements(&self) -> usize {
        match self {
            InferredJsonType::Array { total_elements, .. } => *total_elements,
            InferredJsonType::Object { total_instances, .. } => *total_instances,
            InferredJsonType::String { total_instances, .. } => *total_instances,
            _ => 1,
        }
    }

    /// Check if this type is compatible with another for merging
    pub fn is_compatible_with(&self, other: &InferredJsonType) -> bool {
        match (self, other) {
            (InferredJsonType::Null, _) | (_, InferredJsonType::Null) => true,
            (InferredJsonType::String { .. }, InferredJsonType::String { .. }) => true,
            (InferredJsonType::Number, InferredJsonType::Number) => true,
            (InferredJsonType::Boolean, InferredJsonType::Boolean) => true,
            (InferredJsonType::Array { .. }, InferredJsonType::Array { .. }) => true,
            (InferredJsonType::Object { .. }, InferredJsonType::Object { .. }) => true,
            _ => false,
        }
    }

    /// Validate schema consistency and detect potential issues
    pub fn validate(&self) -> Result<(), String> {
        match self {
            InferredJsonType::Array { element_type, total_elements, array_count, .. } => {
                // Validate array consistency
                if *total_elements == 0 && *array_count > 0 {
                    return Err("Array has count > 0 but total_elements = 0".to_string());
                }
                element_type.validate()?;
            },
            InferredJsonType::Object { fields, total_instances, .. } => {
                // Validate object consistency
                if fields.is_empty() && *total_instances > 0 {
                    return Err("Object has instances > 0 but no fields".to_string());
                }
                for (field_name, field_type) in fields {
                    if field_name.is_empty() {
                        return Err("Object field has empty name".to_string());
                    }
                    field_type.validate()?;
                }
            },
            InferredJsonType::String { total_instances, .. } => {
                if *total_instances == 0 {
                    return Err("String type has zero instances".to_string());
                }
            },
            _ => {}, // Other types are always valid
        }
        Ok(())
    }

    /// Create a compact string representation for debugging
    pub fn to_compact_string(&self) -> String {
        match self {
            InferredJsonType::Null => "null".to_string(),
            InferredJsonType::Boolean => "bool".to_string(),
            InferredJsonType::Number => "num".to_string(),
            InferredJsonType::String { max_length, total_instances } => {
                format!("str(max:{:?},count:{})", max_length, total_instances)
            },
            InferredJsonType::Array { element_type, total_elements, array_count, .. } => {
                format!("arr[{}](elems:{},count:{})",
                    element_type.to_compact_string(), total_elements, array_count)
            },
            InferredJsonType::Object { fields, total_instances } => {
                let field_strs: Vec<String> = fields.iter()
                    .map(|(name, field_type)| format!("{}:{}", name, field_type.to_compact_string()))
                    .collect();
                format!("obj{{{}}}(count:{})",
                    field_strs.join(","), total_instances)
            },
        }
    }
}

impl InferredSchema {
    /// Create a new schema with validation
    pub fn new(root_type: InferredJsonType) -> Result<Self, String> {
        root_type.validate()?;

        let complexity = root_type.calculate_complexity();
        let memory_footprint = root_type.memory_footprint();

        // Calculate total rows based on root type
        let total_rows = match &root_type {
            InferredJsonType::Array { max_length, .. } => {
                // For root arrays, each element becomes a row
                max_length.unwrap_or(1)
            },
            _ => {
                // For non-array root types, there's only one row
                1
            }
        };

        let statistics = SchemaStatistics {
            total_rows,
            max_nesting_depth: Self::calculate_nesting_depth(&root_type),
            unique_field_names: Self::collect_field_names(&root_type),
            schema_complexity_score: complexity,
        };

        // Calculate memory requirements with correct row count
        let calculator = VectorCapacityCalculator::new(root_type.clone(), total_rows);
        let vector_capacities = calculator.calculate_all_capacities()
            .map_err(|e| format!("Failed to calculate capacities: {}", e))?;

        let memory_requirements = MemoryRequirements {
            vector_capacities: vector_capacities.capacities,
            total_memory_estimate: vector_capacities.total_memory,
            peak_memory_estimate: vector_capacities.total_memory * 2,
        };

        Ok(InferredSchema {
            root_type,
            statistics,
            memory_requirements,
        })
    }

    /// Calculate maximum nesting depth in the schema
    fn calculate_nesting_depth(json_type: &InferredJsonType) -> usize {
        match json_type {
            InferredJsonType::Array { element_type, .. } => {
                1 + Self::calculate_nesting_depth(element_type)
            },
            InferredJsonType::Object { fields, .. } => {
                1 + fields.iter()
                    .map(|(_, field_type)| Self::calculate_nesting_depth(field_type))
                    .max()
                    .unwrap_or(0)
            },
            _ => 0,
        }
    }

    /// Collect all unique field names in the schema
    fn collect_field_names(json_type: &InferredJsonType) -> HashSet<String> {
        let mut field_names = HashSet::new();
        Self::collect_field_names_recursive(json_type, &mut field_names);
        field_names
    }

    fn collect_field_names_recursive(json_type: &InferredJsonType, field_names: &mut HashSet<String>) {
        match json_type {
            InferredJsonType::Array { element_type, .. } => {
                Self::collect_field_names_recursive(element_type, field_names);
            },
            InferredJsonType::Object { fields, .. } => {
                for (field_name, field_type) in fields {
                    field_names.insert(field_name.clone());
                    Self::collect_field_names_recursive(field_type, field_names);
                }
            },
            _ => {},
        }
    }

    /// Get a summary of the schema for debugging
    pub fn summary(&self) -> String {
        format!(
            "Schema Summary:\n  Root: {}\n  Complexity: {}\n  Memory Footprint: {} bytes\n  Max Depth: {}\n  Unique Fields: {}\n  Total Memory: {} bytes",
            self.root_type.to_compact_string(),
            self.statistics.schema_complexity_score,
            self.root_type.memory_footprint(),
            self.statistics.max_nesting_depth,
            self.statistics.unique_field_names.len(),
            self.memory_requirements.total_memory_estimate
        )
    }
}

impl VectorPath {
    /// Append a path component to create a new path
    pub fn append(&self, component: PathComponent) -> VectorPath {
        let mut new_components = self.path_components.clone();
        new_components.push(component);
        VectorPath { path_components: new_components }
    }

    /// Create a child path for array elements
    pub fn child_array(&self) -> VectorPath {
        self.append(PathComponent::ArrayElement)
    }

    /// Create a child path for object fields
    pub fn child_field(&self, field_name: &str) -> VectorPath {
        self.append(PathComponent::ObjectField(field_name.to_string()))
    }

    /// Convert path to human-readable string for debugging
    pub fn to_string(&self) -> String {
        self.path_components.iter()
            .map(|component| match component {
                PathComponent::Root => "root".to_string(),
                PathComponent::ArrayElement => "[]".to_string(),
                PathComponent::ObjectField(name) => format!(".{}", name),
            })
            .collect::<Vec<_>>()
            .join("")
    }
}

/// Calculates exact DuckDB vector capacities from inferred schema
pub struct VectorCapacityCalculator {
    schema: InferredJsonType,
    row_count: usize,
}

/// Container for all calculated vector capacities
#[derive(Debug, Clone)]
pub struct VectorCapacities {
    pub capacities: HashMap<VectorPath, usize>,
    pub total_memory: usize,
}

/// Error types for capacity calculation
#[derive(Debug)]
pub enum CapacityError {
    CapacityOverflow { path: String },
    InvalidCapacity { capacity: usize, type_name: String },
}

impl std::fmt::Display for CapacityError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CapacityError::CapacityOverflow { path } => {
                write!(f, "Capacity calculation overflow for path {}", path)
            },
            CapacityError::InvalidCapacity { capacity, type_name } => {
                write!(f, "Invalid capacity: {} for type {}", capacity, type_name)
            },
        }
    }
}

impl std::error::Error for CapacityError {}

impl From<CapacityError> for SchemaInferenceError {
    fn from(error: CapacityError) -> Self {
        SchemaInferenceError::MemoryError(error.to_string())
    }
}

impl VectorCapacityCalculator {
    pub fn new(schema: InferredJsonType, row_count: usize) -> Self {
        Self { schema, row_count }
    }

    /// Calculate exact capacities for all vectors in the schema
    pub fn calculate_all_capacities(&self) -> Result<VectorCapacities, CapacityError> {
        let mut capacities = HashMap::new();
        let root_path = VectorPath { path_components: vec![PathComponent::Root] };

        self.calculate_type_capacity(
            &self.schema,
            &root_path,
            self.row_count,
            &mut capacities
        )?;

        Ok(VectorCapacities {
            capacities: capacities.clone(),
            total_memory: self.calculate_total_memory(&capacities),
        })
    }

    /// Recursive capacity calculation for nested types
    fn calculate_type_capacity(
        &self,
        json_type: &InferredJsonType,
        current_path: &VectorPath,
        instance_count: usize,
        capacities: &mut HashMap<VectorPath, usize>
    ) -> Result<(), CapacityError> {
        match json_type {
            InferredJsonType::Array { element_type, total_elements, .. } => {
                // List vector needs capacity for all elements across all arrays
                let array_capacity = total_elements.checked_mul(instance_count)
                    .ok_or_else(|| CapacityError::CapacityOverflow {
                        path: current_path.to_string()
                    })?;
                capacities.insert(current_path.clone(), array_capacity);

                // Calculate capacity for child elements
                let child_path = current_path.append(PathComponent::ArrayElement);
                self.calculate_type_capacity(
                    element_type,
                    &child_path,
                    array_capacity,
                    capacities
                )?;
            },

            InferredJsonType::Object { fields, total_instances, .. } => {
                // Struct vector capacity is number of struct instances
                let struct_capacity = total_instances.checked_mul(instance_count)
                    .ok_or_else(|| CapacityError::CapacityOverflow {
                        path: current_path.to_string()
                    })?;
                capacities.insert(current_path.clone(), struct_capacity);

                // Calculate capacity for each field
                for (field_name, field_type) in fields {
                    let field_path = current_path.append(PathComponent::ObjectField(field_name.clone()));
                    self.calculate_type_capacity(
                        field_type,
                        &field_path,
                        struct_capacity,
                        capacities
                    )?;
                }
            },

            InferredJsonType::String { total_instances, .. } => {
                // String vector capacity is number of instances
                let string_capacity = total_instances.checked_mul(instance_count)
                    .ok_or_else(|| CapacityError::CapacityOverflow {
                        path: current_path.to_string()
                    })?;
                capacities.insert(current_path.clone(), string_capacity);
            },

            InferredJsonType::Number => {
                // Number vector capacity is number of instances
                capacities.insert(current_path.clone(), instance_count);
            },

            InferredJsonType::Boolean => {
                // Boolean vector capacity is number of instances
                capacities.insert(current_path.clone(), instance_count);
            },

            InferredJsonType::Null => {
                // Null values don't need capacity
                capacities.insert(current_path.clone(), 0);
            },
        }

        Ok(())
    }

    /// Calculate total memory requirement from all capacities
    fn calculate_total_memory(&self, capacities: &HashMap<VectorPath, usize>) -> usize {
        capacities.values().sum::<usize>() * 8 // Assume 8 bytes per element average
    }
}

/// Public API function for schema inference from file
pub fn infer_schema_from_file(
    file_path: &str,
    config: Option<SchemaInferenceConfig>
) -> Result<InferredSchema, SchemaInferenceError> {
    let config = config.unwrap_or_default();
    let mut inferrer = SchemaInferrer::new(config);

    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(SchemaInferenceError::FileError(
            std::io::Error::new(std::io::ErrorKind::NotFound, format!("File does not exist: {}", file_path))
        ));
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = create_json_reader(buf_reader);

    // Infer schema from the JSON file
    let root_type = inferrer.infer_from_stream(&mut json_reader)?;

    // Calculate total rows based on root type
    let total_rows = match &root_type {
        InferredJsonType::Array { max_length, .. } => {
            // For root arrays, each element becomes a row
            max_length.unwrap_or(1)
        },
        _ => {
            // For non-array root types, there's only one row
            1
        }
    };

    // Update statistics with correct total_rows
    let mut updated_statistics = inferrer.statistics;
    updated_statistics.total_rows = total_rows;

    // Calculate exact memory requirements using the correct row count
    let capacity_calculator = VectorCapacityCalculator::new(root_type.clone(), total_rows);
    let vector_capacities = capacity_calculator.calculate_all_capacities()?;

    let memory_requirements = MemoryRequirements {
        vector_capacities: vector_capacities.capacities,
        total_memory_estimate: vector_capacities.total_memory,
        peak_memory_estimate: vector_capacities.total_memory * 2, // Conservative estimate
    };

    Ok(InferredSchema {
        root_type,
        statistics: updated_statistics,
        memory_requirements,
    })
}

/// Test function for vector capacity calculation
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_offset_calculation_logic() {
        // Test our offset calculation logic for nested arrays
        // This tests the algorithm without requiring DuckDB vectors

        // Test data: [[1, 2], [3, 4, 5]]
        let nested_data = vec![
            vec![1.0, 2.0],
            vec![3.0, 4.0, 5.0]
        ];

        // Calculate offsets as our code should do
        let mut offset = 0;
        let mut expected_entries = Vec::new();

        for inner_array in &nested_data {
            expected_entries.push((offset, inner_array.len()));
            offset += inner_array.len();
        }

        // Verify offset calculation
        assert_eq!(expected_entries[0], (0, 2)); // First array: offset=0, length=2
        assert_eq!(expected_entries[1], (2, 3)); // Second array: offset=2, length=3
        assert_eq!(offset, 5); // Total elements

        // Verify flat data layout
        let mut flat_data = Vec::new();
        for inner_array in &nested_data {
            flat_data.extend(inner_array);
        }

        assert_eq!(flat_data, vec![1.0, 2.0, 3.0, 4.0, 5.0]);

        // Test accessing data by offset
        let (first_offset, first_length) = expected_entries[0];
        let first_array_data: Vec<f64> = flat_data[first_offset..first_offset + first_length].to_vec();
        assert_eq!(first_array_data, vec![1.0, 2.0]);

        let (second_offset, second_length) = expected_entries[1];
        let second_array_data: Vec<f64> = flat_data[second_offset..second_offset + second_length].to_vec();
        assert_eq!(second_array_data, vec![3.0, 4.0, 5.0]);
    }

    #[test]
    fn test_vector_path_construction() {
        // Test vector path construction for different scenarios
        let root = VectorPath { path_components: vec![PathComponent::Root] };
        assert_eq!(root.to_string(), "root");

        let array_path = root.child_array();
        assert_eq!(array_path.to_string(), "root[]");

        let field_path = root.child_field("numbers");
        assert_eq!(field_path.to_string(), "root.numbers");

        let nested_path = field_path.child_array();
        assert_eq!(nested_path.to_string(), "root.numbers[]");
    }

    #[test]
    fn test_schema_statistics_calculation() {
        // Test schema statistics for different JSON types

        // Simple array
        let simple_array = InferredJsonType::Array {
            element_type: Box::new(InferredJsonType::Number),
            max_length: Some(5),
            total_elements: 5,
            array_count: 1,
        };

        let depth = InferredSchema::calculate_nesting_depth(&simple_array);
        assert_eq!(depth, 1);

        let fields = InferredSchema::collect_field_names(&simple_array);
        assert_eq!(fields.len(), 0); // Arrays don't have field names

        // Object with fields
        let object_type = InferredJsonType::Object {
            fields: vec![
                ("name".to_string(), InferredJsonType::String { max_length: Some(10), total_instances: 5 }),
                ("age".to_string(), InferredJsonType::Number),
            ],
            total_instances: 2,
        };

        let object_depth = InferredSchema::calculate_nesting_depth(&object_type);
        assert_eq!(object_depth, 1);

        let object_fields = InferredSchema::collect_field_names(&object_type);
        assert_eq!(object_fields.len(), 2);
        assert!(object_fields.contains("name"));
        assert!(object_fields.contains("age"));
    }

    #[test]
    fn test_json_type_memory_footprint() {
        // Test memory footprint calculation for different types

        let number_type = InferredJsonType::Number;
        let number_footprint = number_type.memory_footprint();
        assert!(number_footprint >= 8); // Should be at least f64 size, but may include overhead

        let string_type = InferredJsonType::String { max_length: Some(20), total_instances: 15 };
        let string_footprint = string_type.memory_footprint();
        assert!(string_footprint >= 20); // Should be at least max_length, but may include overhead

        let bool_type = InferredJsonType::Boolean;
        let bool_footprint = bool_type.memory_footprint();
        assert!(bool_footprint >= 1); // Should be at least 1 byte, but may include overhead

        // Array memory footprint includes element overhead
        let array_type = InferredJsonType::Array {
            element_type: Box::new(InferredJsonType::Number),
            max_length: Some(3),
            total_elements: 3,
            array_count: 1,
        };
        let array_footprint = array_type.memory_footprint();
        assert!(array_footprint > 8 * 3); // Should be more than just element size
    }

    #[test]
    fn test_vector_capacity_calculation() -> Result<(), Box<dyn std::error::Error>> {
    // Create a simple test schema: Array of Objects with String and Number fields
    let test_schema = InferredJsonType::Array {
        element_type: Box::new(InferredJsonType::Object {
            fields: vec![
                ("name".to_string(), InferredJsonType::String {
                    max_length: Some(50),
                    total_instances: 3,
                }),
                ("age".to_string(), InferredJsonType::Number),
            ],
            total_instances: 3,
        }),
        max_length: Some(3),
        total_elements: 3,
        array_count: 1,
    };

    let calculator = VectorCapacityCalculator::new(test_schema, 1);
    let capacities = calculator.calculate_all_capacities()?;

    eprintln!("Test Vector Capacities:");
    for (path, capacity) in &capacities.capacities {
        eprintln!("  {}: {}", path.to_string(), capacity);
    }
    eprintln!("Total Memory: {} bytes", capacities.total_memory);

    Ok(())
    }
}

/// Test function for schema representation system
#[cfg(test)]
pub fn test_schema_representation() -> Result<(), Box<dyn std::error::Error>> {
    // Create a complex test schema
    let test_schema = InferredJsonType::Object {
        fields: vec![
            ("users".to_string(), InferredJsonType::Array {
                element_type: Box::new(InferredJsonType::Object {
                    fields: vec![
                        ("name".to_string(), InferredJsonType::String {
                            max_length: Some(50),
                            total_instances: 5,
                        }),
                        ("scores".to_string(), InferredJsonType::Array {
                            element_type: Box::new(InferredJsonType::Number),
                            max_length: Some(3),
                            total_elements: 15,
                            array_count: 5,
                        }),
                    ],
                    total_instances: 5,
                }),
                max_length: Some(5),
                total_elements: 5,
                array_count: 1,
            }),
            ("metadata".to_string(), InferredJsonType::Object {
                fields: vec![
                    ("version".to_string(), InferredJsonType::String {
                        max_length: Some(10),
                        total_instances: 1,
                    }),
                ],
                total_instances: 1,
            }),
        ],
        total_instances: 1,
    };

    // Test schema creation and validation
    let schema = InferredSchema::new(test_schema)?;

    eprintln!("Schema Representation Test:");
    eprintln!("{}", schema.summary());
    eprintln!("Compact representation: {}", schema.root_type.to_compact_string());



    Ok(())
}

/// Schema inference pass for capacity calculation
pub struct SchemaInferencePass {
    inferrer: SchemaInferrer,
    config: SchemaInferenceConfig,
    progress_tracker: ProgressTracker,
}

/// Progress tracking for large file processing
pub struct ProgressTracker {
    file_size: u64,
    bytes_processed: u64,
    start_time: std::time::Instant,
    last_report_time: std::time::Instant,
    enable_reporting: bool,
}

impl ProgressTracker {
    pub fn new(file_size: u64, enable_reporting: bool) -> Self {
        let now = std::time::Instant::now();
        Self {
            file_size,
            bytes_processed: 0,
            start_time: now,
            last_report_time: now,
            enable_reporting,
        }
    }

    pub fn update_progress(&mut self, bytes_processed: u64) {
        self.bytes_processed = bytes_processed;

        if self.enable_reporting {
            let now = std::time::Instant::now();
            if now.duration_since(self.last_report_time).as_secs() >= 5 {
                self.report_progress();
                self.last_report_time = now;
            }
        }
    }

    pub fn report_progress(&self) {
        let percentage = if self.file_size > 0 {
            (self.bytes_processed as f64 / self.file_size as f64) * 100.0
        } else {
            0.0
        };

        let elapsed = self.start_time.elapsed();
        let rate = if elapsed.as_secs() > 0 {
            self.bytes_processed as f64 / elapsed.as_secs() as f64
        } else {
            0.0
        };

        eprintln!("Schema Inference Progress: {:.1}% ({} / {} bytes) - {:.1} bytes/sec",
                 percentage, self.bytes_processed, self.file_size, rate);
    }

    pub fn finish(&self) {
        if self.enable_reporting {
            let elapsed = self.start_time.elapsed();
            eprintln!("Schema Inference Complete: {} bytes processed in {:.2}s",
                     self.bytes_processed, elapsed.as_secs_f64());
        }
    }
}

impl SchemaInferencePass {
    pub fn new(config: SchemaInferenceConfig) -> Self {
        let inferrer = SchemaInferrer::new(config.clone());
        Self {
            inferrer,
            config: config.clone(),
            progress_tracker: ProgressTracker::new(0, config.enable_progress_reporting),
        }
    }

    /// Infer schema from entire JSON file with progress tracking and memory monitoring
    pub fn infer_schema_from_file(&mut self, file_path: &str) -> Result<InferredSchema, SchemaInferenceError> {
        // Check if file exists and get size
        let file_metadata = std::fs::metadata(file_path)
            .map_err(|e| SchemaInferenceError::FileError(e))?;
        let file_size = file_metadata.len();

        self.progress_tracker = ProgressTracker::new(file_size, self.config.enable_progress_reporting);

        if self.config.enable_debug_output {
            eprintln!("Starting schema inference for file: {} ({} bytes)", file_path, file_size);
        }

        // Open file and create JSON reader
        let file = File::open(file_path)?;
        let buf_reader = BufReader::new(file);
        let mut json_reader = create_json_reader(buf_reader);

        // Start memory monitoring
        self.inferrer.memory_tracker.start_monitoring();
        self.inferrer.memory_tracker.record_checkpoint("start_inference");

        // Infer schema from the JSON file
        let root_type = self.infer_complete_structure(&mut json_reader)?;

        // Final memory check
        self.inferrer.memory_tracker.check_memory_bounds()?;
        self.inferrer.memory_tracker.record_checkpoint("inference_complete");

        // Create complete schema with validation
        let schema = InferredSchema::new(root_type)?;

        self.progress_tracker.finish();

        if self.config.enable_debug_output {
            eprintln!("Schema inference completed successfully");
            eprintln!("{}", schema.summary());
        }

        Ok(schema)
    }

    /// Infer complete JSON structure handling root-level arrays and objects
    fn infer_complete_structure(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        match json_reader.peek().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            struson::reader::ValueType::Array => {
                // Root-level array: process all elements to get complete schema
                self.infer_root_array_complete(json_reader)
            },
            struson::reader::ValueType::Object => {
                // Root-level object: process single object
                self.inferrer.infer_value_recursive(json_reader)
            },
            _ => {
                // Single primitive value at root
                self.inferrer.infer_value_recursive(json_reader)
            }
        }
    }

    /// Infer schema from root-level array by processing ALL elements (not just first)
    fn infer_root_array_complete(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        json_reader.begin_array().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        let mut merged_element_type: Option<InferredJsonType> = None;
        let mut element_count: usize = 0;
        let mut total_elements: usize = 0;

        // Process ALL elements in the array for complete schema
        while json_reader.has_next().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            let current_element = self.inferrer.infer_value_recursive(json_reader)?;
            element_count += 1;
            total_elements += current_element.count_total_elements();

            merged_element_type = Some(match merged_element_type {
                None => current_element,
                Some(existing) => self.inferrer.merge_types(existing, current_element)?,
            });

            // Update progress periodically
            if element_count % 1000 == 0 {
                self.progress_tracker.update_progress((element_count * 100) as u64); // Rough estimate
                self.inferrer.memory_tracker.check_memory_bounds()?;
            }

            // Check if we should stop for memory reasons
            if element_count > MAX_ARRAY_ELEMENTS_FOR_INFERENCE {
                if self.config.enable_debug_output {
                    eprintln!("Stopping array inference after {} elements (memory limit)", element_count);
                }
                break;
            }
        }

        json_reader.end_array().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        Ok(InferredJsonType::Array {
            element_type: Box::new(merged_element_type.unwrap_or(InferredJsonType::Null)),
            max_length: Some(element_count),
            total_elements,
            array_count: 1,
        })
    }

    /// Calculate vector capacities from inferred schema
    pub fn calculate_vector_capacities(&self, schema: &InferredSchema) -> VectorCapacities {
        let calculator = VectorCapacityCalculator::new(schema.root_type.clone(), schema.statistics.total_rows);
        calculator.calculate_all_capacities()
            .unwrap_or_else(|_| VectorCapacities {
                capacities: HashMap::new(),
                total_memory: 0,
            })
    }

    /// Get memory usage report from the inference process
    pub fn get_memory_report(&self) -> String {
        let report = self.inferrer.memory_tracker.get_memory_report();
        format!(
            "Memory Usage Report:\n  Baseline: {} bytes\n  Peak: {} bytes\n  Delta: {} bytes\n  Checkpoints: {}",
            report.baseline_memory,
            report.peak_memory,
            report.memory_delta,
            report.checkpoints.len()
        )
    }
}

/// Test function for Pass 1 - Schema Inference Pass
#[cfg(test)]
pub fn test_pass1_schema_inference() -> Result<(), Box<dyn std::error::Error>> {
    // Create a test JSON file content (in memory simulation)
    let test_json = r#"[
        {"name": "Alice", "age": 30, "scores": [95, 87, 92]},
        {"name": "Bob", "age": 25, "scores": [88, 91]},
        {"name": "Charlie", "age": 35, "scores": [93, 89, 94, 96]}
    ]"#;

    // Write test data to a temporary file
    use std::io::Write;
    let mut temp_file = std::fs::File::create("test_schema_inference.json")?;
    temp_file.write_all(test_json.as_bytes())?;
    temp_file.sync_all()?;
    drop(temp_file);

    // Test Pass 1 schema inference
    let config = SchemaInferenceConfig {
        enable_debug_output: true,
        enable_progress_reporting: true,
        ..Default::default()
    };

    let mut pass1 = SchemaInferencePass::new(config);
    let schema = pass1.infer_schema_from_file("test_schema_inference.json")?;

    eprintln!("Pass 1 Test Results:");
    eprintln!("{}", schema.summary());
    eprintln!("Root type: {}", schema.root_type.to_compact_string());

    // Test vector capacity calculation
    let capacities = pass1.calculate_vector_capacities(&schema);
    eprintln!("Vector Capacities:");
    for (path, capacity) in &capacities.capacities {
        eprintln!("  {}: {}", path.to_string(), capacity);
    }

    // Test memory report
    eprintln!("{}", pass1.get_memory_report());

    // Clean up
    std::fs::remove_file("test_schema_inference.json").ok();

    Ok(())
}

/// Streaming data loader with pre-allocated vectors
pub struct StreamingDataLoader {
    schema: InferredSchema,
    vector_capacities: VectorCapacities,
    offset_tracker: CumulativeOffsetTracker,
    config: DataLoaderConfig,
}

/// Configuration for data loading behavior
#[derive(Debug, Clone)]
pub struct DataLoaderConfig {
    pub enable_debug_output: bool,
    pub enable_progress_reporting: bool,
    pub batch_size: usize,
    pub validate_offsets: bool,
}

impl Default for DataLoaderConfig {
    fn default() -> Self {
        Self {
            enable_debug_output: false,
            enable_progress_reporting: false,
            batch_size: 2048, // DuckDB standard vector size
            validate_offsets: true,
        }
    }
}

/// Tracks cumulative offsets across all vectors for proper memory layout
pub struct CumulativeOffsetTracker {
    current_offsets: HashMap<VectorPath, usize>,
    capacity_limits: HashMap<VectorPath, usize>,
    debug_mode: bool,
    allocation_history: Vec<OffsetAllocation>,
}

#[derive(Debug, Clone)]
struct OffsetAllocation {
    vector_path: VectorPath,
    start_offset: usize,
    length: usize,
    row_index: usize,
}

/// Range of offsets allocated for a specific data insertion
#[derive(Debug, Clone)]
pub struct OffsetRange {
    pub start_offset: usize,
    pub length: usize,
    pub end_offset: usize,
}

/// Error types for data loading
#[derive(Debug)]
pub enum DataLoaderError {
    SchemaError(String),
    OffsetError(String),
    CapacityExceeded { vector_path: String, required: usize, available: usize },
    JsonParsingError(String),
    VectorError(String),
}

impl std::fmt::Display for DataLoaderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DataLoaderError::SchemaError(msg) => write!(f, "Schema error: {}", msg),
            DataLoaderError::OffsetError(msg) => write!(f, "Offset error: {}", msg),
            DataLoaderError::CapacityExceeded { vector_path, required, available } => {
                write!(f, "Capacity exceeded for {}: required {}, available {}", vector_path, required, available)
            },
            DataLoaderError::JsonParsingError(msg) => write!(f, "JSON parsing error: {}", msg),
            DataLoaderError::VectorError(msg) => write!(f, "Vector error: {}", msg),
        }
    }
}

impl std::error::Error for DataLoaderError {}

impl CumulativeOffsetTracker {
    pub fn new(vector_capacities: &VectorCapacities, debug_mode: bool) -> Self {
        let mut current_offsets = HashMap::new();
        let mut capacity_limits = HashMap::new();

        // Initialize all vector paths with zero offset and their capacity limits
        for (vector_path, capacity) in &vector_capacities.capacities {
            current_offsets.insert(vector_path.clone(), 0);
            capacity_limits.insert(vector_path.clone(), *capacity);
        }

        Self {
            current_offsets,
            capacity_limits,
            debug_mode,
            allocation_history: Vec::new(),
        }
    }

    /// Allocate a range of offsets for a specific vector path
    pub fn allocate_range(&mut self, vector_path: &VectorPath, length: usize, row_index: usize) -> Result<OffsetRange, DataLoaderError> {
        let current_offset = self.current_offsets.get(vector_path).copied().unwrap_or(0);
        let capacity_limit = self.capacity_limits.get(vector_path).copied().unwrap_or(0);

        // Check capacity bounds
        if current_offset + length > capacity_limit {
            return Err(DataLoaderError::CapacityExceeded {
                vector_path: vector_path.to_string(),
                required: current_offset + length,
                available: capacity_limit,
            });
        }

        let range = OffsetRange {
            start_offset: current_offset,
            length,
            end_offset: current_offset + length,
        };

        // Update current offset
        self.current_offsets.insert(vector_path.clone(), current_offset + length);

        // Record allocation for debugging
        if self.debug_mode {
            self.allocation_history.push(OffsetAllocation {
                vector_path: vector_path.clone(),
                start_offset: current_offset,
                length,
                row_index,
            });
        }

        Ok(range)
    }

    /// Validate that all capacity usage is within bounds
    pub fn validate_capacity_usage(&self) -> Result<(), DataLoaderError> {
        for (vector_path, current_offset) in &self.current_offsets {
            let capacity_limit = self.capacity_limits.get(vector_path).copied().unwrap_or(0);
            if *current_offset > capacity_limit {
                return Err(DataLoaderError::CapacityExceeded {
                    vector_path: vector_path.to_string(),
                    required: *current_offset,
                    available: capacity_limit,
                });
            }
        }
        Ok(())
    }

    /// Get debug report of offset allocations
    pub fn get_debug_report(&self) -> String {
        let mut report = String::new();
        report.push_str("Cumulative Offset Tracker Report:\n");

        for (vector_path, current_offset) in &self.current_offsets {
            let capacity_limit = self.capacity_limits.get(vector_path).copied().unwrap_or(0);
            let usage_percent = if capacity_limit > 0 {
                (*current_offset as f64 / capacity_limit as f64) * 100.0
            } else {
                0.0
            };

            report.push_str(&format!(
                "  {}: {}/{} ({:.1}%)\n",
                vector_path.to_string(),
                current_offset,
                capacity_limit,
                usage_percent
            ));
        }

        if self.debug_mode && !self.allocation_history.is_empty() {
            report.push_str("\nAllocation History:\n");
            for allocation in &self.allocation_history {
                report.push_str(&format!(
                    "  Row {}: {} [{}..{}] (len={})\n",
                    allocation.row_index,
                    allocation.vector_path.to_string(),
                    allocation.start_offset,
                    allocation.start_offset + allocation.length,
                    allocation.length
                ));
            }
        }

        report
    }
}

impl StreamingDataLoader {
    pub fn new(schema: InferredSchema, vector_capacities: VectorCapacities, config: DataLoaderConfig) -> Self {
        let offset_tracker = CumulativeOffsetTracker::new(&vector_capacities, config.enable_debug_output);

        Self {
            schema,
            vector_capacities,
            offset_tracker,
            config,
        }
    }

    /// Load JSON data directly into pre-allocated DuckDB vectors using known schema
    pub fn load_data_from_file(
        &mut self,
        file_path: &str,
        output: &DataChunkHandle,
        column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        if self.config.enable_debug_output {
            eprintln!("Pass 2: Starting data loading for file: {}", file_path);
            eprintln!("Schema: {}", self.schema.summary());
        }

        // Open file and create JSON reader
        let file = File::open(file_path)
            .map_err(|e| DataLoaderError::JsonParsingError(format!("Failed to open file: {}", e)))?;
        let buf_reader = BufReader::new(file);
        let mut json_reader = create_json_reader(buf_reader);

        // Load data based on root schema type
        let rows_processed = match &self.schema.root_type {
            InferredJsonType::Array { .. } => {
                self.load_root_array_data(&mut json_reader, output, column_index)?
            },
            InferredJsonType::Object { .. } => {
                self.load_single_object_data(&mut json_reader, output, column_index)?
            },
            _ => {
                self.load_single_primitive_data(&mut json_reader, output, column_index)?
            }
        };

        // Validate offset usage
        self.offset_tracker.validate_capacity_usage()?;

        if self.config.enable_debug_output {
            eprintln!("Pass 2: Completed data loading. Rows processed: {}", rows_processed);
            eprintln!("{}", self.offset_tracker.get_debug_report());
        }

        Ok(rows_processed)
    }

    /// Load data from root-level JSON array (flattened into rows)
    fn load_root_array_data(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        _column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        json_reader.begin_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        let mut row_count = 0;

        // For root arrays, we flatten each array element into a row
        // Each element should be an object whose fields map to columns
        if let InferredJsonType::Array { element_type, .. } = &self.schema.root_type {
            if let InferredJsonType::Object { fields, .. } = element_type.as_ref() {
                // Clone fields to avoid borrowing issues
                let fields_clone = fields.clone();

                // Process each array element as a row
                while json_reader.has_next()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                    self.load_object_as_flattened_row(json_reader, output, row_count, &fields_clone)?;
                    row_count += 1;

                    // Progress reporting
                    if self.config.enable_progress_reporting && row_count % 1000 == 0 {
                        eprintln!("Pass 2: Processed {} rows", row_count);
                    }
                }
            } else {
                // Array of non-objects - each element becomes a row with single 'value' column
                let element_type_clone = element_type.as_ref().clone();

                while json_reader.has_next()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                    // Use the appropriate vector type based on element type
                    match &element_type_clone {
                        InferredJsonType::Array { element_type: inner_type, .. } => {
                            // For arrays, use list vector and parse the array data
                            let mut list_vector = output.list_vector(0);
                            let array_elements = self.parse_nested_array_structure(json_reader, &element_type_clone)?;
                            // Create root vector path for this array
                            let root_path = VectorPath { path_components: vec![PathComponent::Root] };
                            self.insert_array_into_list_vector(&mut list_vector, row_count, &array_elements, inner_type, &root_path)?;
                        },
                        InferredJsonType::Object { fields, .. } => {
                            // For objects, use struct vector and parse the object data
                            let mut struct_vector = output.struct_vector(0);
                            let object_fields = self.parse_object_in_array(json_reader, fields)?;
                            // For root array elements, each element gets its own row, so capacity is 1
                            let root_path = VectorPath { path_components: vec![PathComponent::Root] };
                            self.insert_object_into_struct_vector(&mut struct_vector, row_count, &object_fields, fields, 1, &root_path)?;
                        },
                        _ => {
                            // For primitives, use flat vector
                            let mut flat_vector = output.flat_vector(0);
                            self.load_primitive_into_vector(json_reader, &mut flat_vector, row_count, &element_type_clone)?;
                        }
                    }
                    row_count += 1;

                    // Progress reporting
                    if self.config.enable_progress_reporting && row_count % 1000 == 0 {
                        eprintln!("Pass 2: Processed {} rows", row_count);
                    }
                }
            }
        }

        json_reader.end_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        Ok(row_count)
    }

    /// Load object as flattened row (each field becomes a column)
    fn load_object_as_flattened_row(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        row_index: usize,
        expected_fields: &Vec<(String, InferredJsonType)>,
    ) -> Result<(), DataLoaderError> {
        json_reader.begin_object()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        // Create a map to track which fields we've seen
        let mut field_values: std::collections::HashMap<String, ParsedValue> = std::collections::HashMap::new();

        // Read all fields from the JSON object
        while json_reader.has_next()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            let field_name = json_reader.next_name()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?
                .to_string();

            // Find the expected type for this field
            if let Some((_, expected_type)) = expected_fields.iter().find(|(name, _)| name == &field_name) {
                let value = self.parse_value_by_type(json_reader, expected_type)?;
                field_values.insert(field_name, value);
            } else {
                // Skip unknown fields
                json_reader.skip_value()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            }
        }

        json_reader.end_object()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        // Now insert the field values into their respective columns
        // For now, handle complex types by converting to string (like empty objects)
        for (col_index, (field_name, expected_type)) in expected_fields.iter().enumerate() {
            let mut flat_vector = output.flat_vector(col_index);

            if let Some(value) = field_values.get(field_name) {
                // For complex types (arrays, objects), we need special handling
                match (value, expected_type) {
                    (ParsedValue::Array(array_elements), InferredJsonType::Array { element_type, .. }) => {
                        // Handle array fields in objects - create list vector
                        let mut list_vector = output.list_vector(col_index);
                        // Create correct field path for this array field (root.fieldname)
                        let field_path = VectorPath {
                            path_components: vec![
                                PathComponent::Root,
                                PathComponent::ObjectField(field_name.clone())
                            ]
                        };
                        self.insert_array_into_list_vector(&mut list_vector, row_index, array_elements, element_type, &field_path)?;
                    },
                    (ParsedValue::Object(object_fields), InferredJsonType::Object { fields, .. }) => {
                        // Handle object fields in objects - create struct vector
                        let mut struct_vector = output.struct_vector(col_index);
                        // Create correct field path for this object field (root.fieldname)
                        let field_path = VectorPath {
                            path_components: vec![
                                PathComponent::Root,
                                PathComponent::ObjectField(field_name.clone())
                            ]
                        };
                        self.insert_object_into_struct_vector(&mut struct_vector, row_index, object_fields, fields, 1, &field_path)?;
                    },
                    _ => {
                        // For primitives, use flat vector
                        self.insert_parsed_value_into_flat_vector(&mut flat_vector, row_index, value)?;
                    }
                }
            } else {
                // Field not present in JSON - set as null
                flat_vector.set_null(row_index);
            }
        }

        Ok(())
    }

    /// Load data from single JSON object (non-array root) - flattened into columns
    fn load_single_object_data(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        _column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        // For root objects, we flatten each field into a column (similar to root arrays)
        if let InferredJsonType::Object { fields, .. } = &self.schema.root_type {
            // Clone fields to avoid borrowing issues
            let fields_clone = fields.clone();

            if fields_clone.is_empty() {
                // Empty object: insert "{}" into the single "json" column
                let mut flat_vector = output.flat_vector(0);
                flat_vector.insert(0, "{}");
            } else {
                // Non-empty object: load as flattened row
                self.load_object_as_flattened_row(json_reader, output, 0, &fields_clone)?;
            }
            Ok(1)
        } else {
            Err(DataLoaderError::SchemaError("Expected object type for single object data".to_string()))
        }
    }

    /// Load data from single primitive value (non-array, non-object root)
    fn load_single_primitive_data(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        // For single primitive, create one row
        let mut flat_vector = output.flat_vector(column_index);
        let root_type = self.schema.root_type.clone();
        self.load_primitive_into_vector(json_reader, &mut flat_vector, 0, &root_type)?;
        Ok(1)
    }

    /// Parse nested array structure recursively
    fn parse_nested_array_structure(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        element_type: &InferredJsonType,
    ) -> Result<Vec<ParsedValue>, DataLoaderError> {
        if let InferredJsonType::Array { element_type: inner_type, .. } = element_type {
            json_reader.begin_array()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            let mut sub_elements = Vec::new();
            while json_reader.has_next()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                match inner_type.as_ref() {
                    InferredJsonType::Array { .. } => {
                        // Recursively parse deeper arrays
                        let deeper_array = self.parse_nested_array_structure(json_reader, inner_type)?;
                        sub_elements.push(ParsedValue::Array(deeper_array));
                    },
                    InferredJsonType::Object { fields, .. } => {
                        // Parse object in array context
                        let object_value = self.parse_object_in_array(json_reader, fields)?;
                        sub_elements.push(ParsedValue::Object(object_value));
                    },
                    _ => {
                        // Parse primitive values directly without going through parse_value_by_type
                        let value = match json_reader.peek()
                            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                            struson::reader::ValueType::Null => {
                                json_reader.next_null()
                                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                                ParsedValue::Null
                            },
                            struson::reader::ValueType::Boolean => {
                                let val = json_reader.next_bool()
                                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                                ParsedValue::Boolean(val)
                            },
                            struson::reader::ValueType::Number => {
                                let val_str = json_reader.next_number_as_str()
                                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                                let val = val_str.parse::<f64>()
                                    .map_err(|e| DataLoaderError::JsonParsingError(format!("Invalid number: {}", e)))?;
                                ParsedValue::Number(val)
                            },
                            struson::reader::ValueType::String => {
                                let val = json_reader.next_string()
                                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                                ParsedValue::String(val)
                            },
                            struson::reader::ValueType::Array => {
                                return Err(DataLoaderError::JsonParsingError("Unexpected nested array in primitive parsing".to_string()));
                            },
                            struson::reader::ValueType::Object => {
                                return Err(DataLoaderError::JsonParsingError("Unexpected object in primitive parsing".to_string()));
                            },
                        };
                        sub_elements.push(value);
                    }
                }
            }

            json_reader.end_array()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            Ok(sub_elements)
        } else {
            Err(DataLoaderError::SchemaError("Expected array type for nested parsing".to_string()))
        }
    }
}

/// Parsed value types for intermediate storage during loading
#[derive(Debug, Clone)]
enum ParsedValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
    Array(Vec<ParsedValue>),
    Object(HashMap<String, ParsedValue>),
}

impl StreamingDataLoader {
    /// Load object data into struct vector
    fn load_object_into_struct(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        struct_vector: &mut duckdb::core::StructVector,
        row_index: usize,
        object_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        if let InferredJsonType::Object { fields, .. } = object_type {
            json_reader.begin_object()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            // Parse all fields from JSON
            let mut parsed_fields = HashMap::new();
            while json_reader.has_next()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                let field_name = json_reader.next_name()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?
                    .to_string();

                if let Some((_, field_type)) = fields.iter().find(|(name, _)| name == &field_name) {
                    let field_value = self.parse_value_by_type(json_reader, field_type)?;
                    parsed_fields.insert(field_name, field_value);
                } else {
                    // Skip unknown field
                    self.skip_json_value(json_reader)?;
                }
            }

            json_reader.end_object()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            // Insert parsed fields into struct vector
            for (field_index, (field_name, field_type)) in fields.iter().enumerate() {
                let mut field_vector = struct_vector.child(field_index, 1); // Capacity of 1 for single row

                if let Some(parsed_value) = parsed_fields.get(field_name) {
                    self.insert_parsed_value_into_vector(&mut field_vector, row_index, parsed_value, field_type)?;
                } else {
                    // Field not present in JSON - set as null
                    field_vector.set_null(row_index);
                }
            }
        } else {
            return Err(DataLoaderError::SchemaError("Expected object type".to_string()));
        }

        Ok(())
    }

    /// Load primitive value into flat vector
    fn load_primitive_into_vector(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        flat_vector: &mut duckdb::core::FlatVector,
        row_index: usize,
        value_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        let parsed_value = self.parse_value_by_type(json_reader, value_type)?;
        self.insert_parsed_value_into_flat_vector(flat_vector, row_index, &parsed_value)?;
        Ok(())
    }

    /// Parse JSON value according to expected type
    fn parse_value_by_type(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        expected_type: &InferredJsonType,
    ) -> Result<ParsedValue, DataLoaderError> {
        match json_reader.peek()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            struson::reader::ValueType::Null => {
                json_reader.next_null()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                Ok(ParsedValue::Null)
            },

            struson::reader::ValueType::Boolean => {
                let value = json_reader.next_bool()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                Ok(ParsedValue::Boolean(value))
            },

            struson::reader::ValueType::Number => {
                let value_str = json_reader.next_number_as_str()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                let value = value_str.parse::<f64>()
                    .map_err(|e| DataLoaderError::JsonParsingError(format!("Invalid number: {}", e)))?;
                Ok(ParsedValue::Number(value))
            },

            struson::reader::ValueType::String => {
                let value = json_reader.next_string()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                Ok(ParsedValue::String(value))
            },

            struson::reader::ValueType::Array => {
                let array_elements = self.parse_nested_array_structure(json_reader, expected_type)?;
                Ok(ParsedValue::Array(array_elements))
            },

            struson::reader::ValueType::Object => {
                if let InferredJsonType::Object { fields, .. } = expected_type {
                    json_reader.begin_object()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

                    let mut object_fields = HashMap::new();
                    while json_reader.has_next()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                        let field_name = json_reader.next_name()
                            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?
                            .to_string();

                        if let Some((_, field_type)) = fields.iter().find(|(name, _)| name == &field_name) {
                            let field_value = self.parse_value_by_type(json_reader, field_type)?;
                            object_fields.insert(field_name, field_value);
                        } else {
                            self.skip_json_value(json_reader)?;
                        }
                    }

                    json_reader.end_object()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

                    Ok(ParsedValue::Object(object_fields))
                } else {
                    Err(DataLoaderError::SchemaError("Expected object type for JSON object".to_string()))
                }
            },
        }
    }

    /// Parse primitive value (simplified version)
    fn parse_primitive_value(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        expected_type: &InferredJsonType,
    ) -> Result<ParsedValue, DataLoaderError> {
        self.parse_value_by_type(json_reader, expected_type)
    }

    /// Skip a JSON value without parsing it
    fn skip_json_value(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
    ) -> Result<(), DataLoaderError> {
        match json_reader.peek()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            struson::reader::ValueType::Null => {
                json_reader.next_null()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::Boolean => {
                json_reader.next_bool()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::Number => {
                json_reader.next_number_as_str()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::String => {
                json_reader.next_string()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::Array => {
                json_reader.begin_array()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                while json_reader.has_next()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {
                    self.skip_json_value(json_reader)?;
                }
                json_reader.end_array()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::Object => {
                json_reader.begin_object()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                while json_reader.has_next()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {
                    json_reader.next_name()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                    self.skip_json_value(json_reader)?;
                }
                json_reader.end_object()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
        }
        Ok(())
    }

    /// Insert parsed value into flat vector using correct DuckDB types
    fn insert_parsed_value_into_flat_vector(
        &mut self,
        flat_vector: &mut duckdb::core::FlatVector,
        row_index: usize,
        parsed_value: &ParsedValue,
    ) -> Result<(), DataLoaderError> {


        match parsed_value {
            ParsedValue::Null => {
                flat_vector.set_null(row_index);
                Ok(())
            },
            ParsedValue::Boolean(b) => {
                // Use direct memory access for boolean values
                flat_vector.as_mut_slice::<bool>()[row_index] = *b;
                Ok(())
            },
            ParsedValue::Number(n) => {
                // Use direct memory access for numeric values (Double type)
                flat_vector.as_mut_slice::<f64>()[row_index] = *n;
                Ok(())
            },
            ParsedValue::String(s) => {
                // Use insert method for string values (Varchar type)
                flat_vector.insert(row_index, s.as_str());
                Ok(())
            },
            ParsedValue::Array(_) => {
                // Arrays should not be inserted into flat vectors in this context
                Err(DataLoaderError::SchemaError("Cannot insert array into flat vector".to_string()))
            },
            ParsedValue::Object(_) => {
                // Objects should not be inserted into flat vectors in this context
                Err(DataLoaderError::SchemaError("Cannot insert object into flat vector".to_string()))
            },
        }
    }

    /// Insert array data into list vector
    fn insert_array_into_list_vector(
        &mut self,
        list_vector: &mut duckdb::core::ListVector,
        row_index: usize,
        array_elements: &[ParsedValue],
        element_type: &InferredJsonType,
        vector_path: &VectorPath,
    ) -> Result<(), DataLoaderError> {


        // Allocate proper offset range for this array
        let offset_range = self.offset_tracker.allocate_range(vector_path, array_elements.len(), row_index)?;

        // Set the list entry for this row with proper offset
        list_vector.set_entry(row_index, offset_range.start_offset, array_elements.len());

        // Get appropriate child vector based on element type
        match element_type {
            InferredJsonType::Array { element_type: inner_type, .. } => {
                // Nested array - CRITICAL: use list_child(), not child()
                let mut child_list_vector = list_vector.list_child();
                for (element_index, element) in array_elements.iter().enumerate() {
                    if let ParsedValue::Array(inner_elements) = element {
                        // Create child vector path for nested arrays
                        let child_path = vector_path.child_array();
                        // For nested arrays, use offset + element_index to maintain proper positioning
                        let child_row_index = offset_range.start_offset + element_index;
                        self.insert_array_into_list_vector(&mut child_list_vector, child_row_index, inner_elements, inner_type, &child_path)?;
                    }
                }
            },
            InferredJsonType::Object { fields, .. } => {
                // Array of objects - get struct child
                let mut child_struct_vector = list_vector.struct_child(array_elements.len());
                for (element_index, element) in array_elements.iter().enumerate() {
                    if let ParsedValue::Object(object_fields) = element {
                        // For array of objects, capacity should be the total number of array elements
                        // Create child vector path for array elements
                        let child_path = vector_path.child_array();
                        // For array of objects, use element_index as row index (not offset)
                        self.insert_object_into_struct_vector(&mut child_struct_vector, element_index, object_fields, fields, array_elements.len(), &child_path)?;
                    }
                }
            },
            _ => {
                // Primitive elements - get flat child and insert data
                // CRITICAL: For primitives, we need to use the total capacity, not just array length
                let child_path = vector_path.child_array();
                let total_capacity = self.offset_tracker.capacity_limits.get(&child_path).copied().unwrap_or(array_elements.len());
                let mut child_vector = list_vector.child(total_capacity);
                for (element_index, element) in array_elements.iter().enumerate() {
                    // For primitive elements, use offset + element_index to write to correct position
                    let actual_position = offset_range.start_offset + element_index;
                    self.insert_parsed_value_into_flat_vector(&mut child_vector, actual_position, element)?;
                }
            }
        }

        Ok(())
    }

    /// Insert object data into struct vector
    fn insert_object_into_struct_vector(
        &mut self,
        struct_vector: &mut duckdb::core::StructVector,
        row_index: usize,
        object_fields: &HashMap<String, ParsedValue>,
        expected_fields: &[(String, InferredJsonType)],
        total_capacity: usize,
        vector_path: &VectorPath,
    ) -> Result<(), DataLoaderError> {
        // Insert each field into the corresponding struct field vector
        for (field_index, (field_name, field_type)) in expected_fields.iter().enumerate() {
            if let Some(field_value) = object_fields.get(field_name) {
                match field_type {
                    InferredJsonType::Array { element_type, .. } => {
                        // Field is an array - get list vector child
                        if let ParsedValue::Array(array_elements) = field_value {
                            let mut list_vector = struct_vector.list_vector_child(field_index);
                            // Create child vector path for this array field
                            let field_path = vector_path.child_field(field_name);



                            self.insert_array_into_list_vector(&mut list_vector, row_index, array_elements, element_type, &field_path)?;
                        }
                    },
                    InferredJsonType::Object { fields: nested_fields, .. } => {
                        // Field is an object - get struct vector child
                        if let ParsedValue::Object(nested_object_fields) = field_value {
                            let mut nested_struct_vector = struct_vector.struct_vector_child(field_index);
                            // Create child vector path for this object field
                            let field_path = vector_path.child_field(field_name);
                            self.insert_object_into_struct_vector(&mut nested_struct_vector, row_index, nested_object_fields, nested_fields, total_capacity, &field_path)?;
                        }
                    },
                    _ => {
                        // Field is a primitive - get flat vector child with proper capacity
                        let mut field_vector = struct_vector.child(field_index, total_capacity);
                        self.insert_parsed_value_into_flat_vector(&mut field_vector, row_index, field_value)?;
                    }
                }
            } else {
                // Field not present in JSON - set as null on the flat vector child
                let mut field_vector = struct_vector.child(field_index, total_capacity);
                field_vector.set_null(row_index);
            }
        }

        Ok(())
    }

    /// Parse object in array context
    fn parse_object_in_array(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        expected_fields: &[(String, InferredJsonType)],
    ) -> Result<HashMap<String, ParsedValue>, DataLoaderError> {
        json_reader.begin_object()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        let mut object_fields = HashMap::new();

        while json_reader.has_next()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            let field_name = json_reader.next_name()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            // Find the expected field type
            let field_type = expected_fields.iter()
                .find(|(name, _)| name == &field_name)
                .map(|(_, field_type)| field_type)
                .ok_or_else(|| DataLoaderError::SchemaError(format!("Unexpected field '{}' in object", field_name)))?;

            // Clone field name to avoid borrowing issues
            let field_name_owned = field_name.to_string();

            // Parse the field value based on its expected type
            let field_value = self.parse_value_by_type(json_reader, field_type)?;
            object_fields.insert(field_name_owned, field_value);
        }

        json_reader.end_object()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        Ok(object_fields)
    }

    /// Insert parsed value into any vector type
    fn insert_parsed_value_into_vector(
        &mut self,
        _vector: &mut dyn std::any::Any,
        _row_index: usize,
        parsed_value: &ParsedValue,
        _expected_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        // This is a simplified implementation - in practice would need proper type dispatch
        match parsed_value {
            ParsedValue::Null => {
                // Set null - would need proper vector type handling
                Ok(())
            },
            ParsedValue::Number(_n) => {
                // Insert number - would need proper vector type handling
                Ok(())
            },
            ParsedValue::String(_s) => {
                // Insert string - would need proper vector type handling
                Ok(())
            },
            ParsedValue::Boolean(_b) => {
                // Insert boolean - would need proper vector type handling
                Ok(())
            },
            _ => {
                // Complex types would need recursive handling
                Ok(())
            }
        }
    }

}

/// Test function for Pass 2 - Streaming Data Loader
#[cfg(test)]
pub fn test_pass2_streaming_data_loader() -> Result<(), Box<dyn std::error::Error>> {
    // Create a test JSON file content
    let test_json = r#"[
        {"name": "Alice", "age": 30},
        {"name": "Bob", "age": 25},
        {"name": "Charlie", "age": 35}
    ]"#;

    // Write test data to a temporary file
    use std::io::Write;
    let mut temp_file = std::fs::File::create("test_pass2_data_loader.json")?;
    temp_file.write_all(test_json.as_bytes())?;
    temp_file.sync_all()?;
    drop(temp_file);

    // First, run Pass 1 to get schema
    let config = SchemaInferenceConfig {
        enable_debug_output: true,
        ..Default::default()
    };

    let mut pass1 = SchemaInferencePass::new(config);
    let schema = pass1.infer_schema_from_file("test_pass2_data_loader.json")?;

    eprintln!("Pass 2 Test - Inferred Schema:");
    eprintln!("{}", schema.summary());

    // Calculate vector capacities
    let capacities = pass1.calculate_vector_capacities(&schema);

    // Test Pass 2 data loading
    let loader_config = DataLoaderConfig {
        enable_debug_output: true,
        ..Default::default()
    };

    let _pass2 = StreamingDataLoader::new(schema, capacities, loader_config);

    // Create a mock output chunk for testing
    // Note: This would normally be provided by DuckDB
    eprintln!("Pass 2 Test: StreamingDataLoader created successfully");
    eprintln!("Pass 2 Test: Would load data from file in real implementation");

    // Clean up
    std::fs::remove_file("test_pass2_data_loader.json").ok();

    Ok(())
}

// ============================================================================
// SCHEMA DISCOVERY SYSTEM
// ============================================================================

#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    inferred_schema: InferredSchema,
    vector_capacities: VectorCapacities,
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    projected_columns: Vec<usize>, // Columns requested by the query
}

struct JsonReaderVTab;

// Generate DuckDB columns directly from InferredJsonType
fn generate_duckdb_columns_from_inferred_schema(inferred_type: &InferredJsonType) -> Result<Vec<(String, LogicalTypeHandle)>, Box<dyn std::error::Error>> {
    match inferred_type {
        InferredJsonType::Array { element_type, .. } => {
            // For root arrays, flatten elements into rows
            match element_type.as_ref() {
                InferredJsonType::Object { fields, .. } => {
                    // Array of objects: each object field becomes a column
                    let mut columns = Vec::new();
                    for (field_name, field_type) in fields {
                        let logical_type = field_type.to_duckdb_type()?;
                        columns.push((field_name.clone(), logical_type));
                    }
                    Ok(columns)
                },
                _ => {
                    // Array of primitives: single 'value' column
                    let logical_type = element_type.to_duckdb_type()?;
                    Ok(vec![("value".to_string(), logical_type)])
                }
            }
        },
        InferredJsonType::Object { fields, .. } => {
            // For objects, create columns for each field
            let mut columns = Vec::new();
            for (field_name, field_type) in fields {
                let logical_type = field_type.to_duckdb_type()?;
                columns.push((field_name.clone(), logical_type));
            }

            // Handle empty objects by creating a single "json" column
            // DuckDB requires table functions to return at least one column
            if columns.is_empty() {
                let varchar_type = LogicalTypeHandle::from(LogicalTypeId::Varchar);
                Ok(vec![("json".to_string(), varchar_type)])
            } else {
                Ok(columns)
            }
        },
        _ => {
            // For primitive types at root, create a single 'value' column
            let logical_type = inferred_type.to_duckdb_type()?;
            Ok(vec![("value".to_string(), logical_type)])
        }
    }
}








impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        eprintln!("DEBUG BIND: File path: {}", file_path);
        eprintln!("DEBUG BIND: Parameter count: {}", bind.get_parameter_count());

        // Try to explore what other information might be available in bind phase
        eprintln!("DEBUG BIND: Exploring BindInfo for additional projection information...");

        // Let's see if BindInfo has any methods that might give us nested field information
        // This is exploratory to understand what's available
        eprintln!("DEBUG BIND: Checking for nested field projection capabilities...");

        // Use schema inference system
        let inferred_schema = infer_schema_from_file(&file_path, None)
            .map_err(|e| format!("Schema inference failed: {}", e))?;

        // Calculate vector capacities
        let pass1 = SchemaInferencePass::new(SchemaInferenceConfig::default());
        let vector_capacities = pass1.calculate_vector_capacities(&inferred_schema);

        // Generate DuckDB columns from inferred schema
        let duckdb_columns = generate_duckdb_columns_from_inferred_schema(&inferred_schema.root_type)?;

        // Add result columns to DuckDB
        for (column_name, logical_type) in duckdb_columns.into_iter() {
            bind.add_result_column(&column_name, logical_type);
        }

        Ok(JsonReaderBindData {
            file_path,
            inferred_schema,
            vector_capacities,
        })
    }

    fn init(init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Get column indices for projection pushdown
        let column_indices = init.get_column_indices();

        // Get bind data to access column names
        let bind_data = unsafe { &*(init.get_bind_data() as *const JsonReaderBindData) };
        let duckdb_columns = generate_duckdb_columns_from_inferred_schema(&bind_data.inferred_schema.root_type)
            .map_err(|e| format!("Failed to generate columns: {}", e))?;
        let column_names: Vec<String> = duckdb_columns.iter().map(|(name, _)| name.clone()).collect();

        // Map indices to actual column names
        let projected_column_names: Vec<String> = column_indices
            .iter()
            .map(|&idx| {
                if (idx as usize) < column_names.len() {
                    column_names[idx as usize].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", idx)
                }
            })
            .collect();

        eprintln!("DEBUG: Projected column names: {:?}", projected_column_names);

        // Try to explore what other methods might be available on InitInfo
        eprintln!("DEBUG: Exploring InitInfo methods...");

        // Let's try to see if there are any other methods we can call on InitInfo
        // This is exploratory - we'll see what's available

        // Check if there's any way to get more detailed projection information
        eprintln!("DEBUG: Checking for additional projection information...");

        // Let's see if there are any other methods we can call
        // (This is exploratory - some might not exist)

        if column_indices.is_empty() {
            eprintln!("DEBUG: No specific columns projected (SELECT * query?)");
        } else {
            eprintln!("DEBUG: Specific columns requested: {:?}", projected_column_names);
        }

        // Convert column indices to simple usize vector
        let projected_columns: Vec<usize> = column_indices
            .iter()
            .map(|&idx| idx as usize)
            .collect();

        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
            projected_columns,
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = unsafe { &*(func.get_bind_data() as *const JsonReaderBindData) };

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        eprintln!("DEBUG FUNC: Processing file: {}", bind_data.file_path);

        // Debug: Print vector capacities before creating data loader
        eprintln!("DEBUG FUNC: Vector capacities being passed to data loader:");
        for (path, capacity) in &bind_data.vector_capacities.capacities {
            eprintln!("  {}: {}", path.to_string(), capacity);
        }
        eprintln!("DEBUG FUNC: Total vector capacity entries: {}", bind_data.vector_capacities.capacities.len());

        // Create streaming data loader
        let mut data_loader = StreamingDataLoader::new(
            bind_data.inferred_schema.clone(),
            bind_data.vector_capacities.clone(),
            DataLoaderConfig {
                enable_debug_output: true,
                enable_progress_reporting: true,
                ..Default::default()
            }
        );

        // Load data from file
        match data_loader.load_data_from_file(&bind_data.file_path, output, 0) {
            Ok(row_count) => {
                eprintln!("DEBUG FUNC: Successfully processed {} rows", row_count);

                if row_count == 0 {
                    output.set_len(0);
                } else {
                    output.set_len(row_count);
                    eprintln!("DEBUG FUNC: Set output length to {} rows", row_count);
                }

                // Mark as finished after processing the data
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished");
            }
            Err(e) => {
                eprintln!("ERROR: Failed to read JSON file '{}': {:?}", bind_data.file_path, e);

                // Provide helpful error messages based on error type
                let error_msg = match e {
                    DataLoaderError::JsonParsingError(msg) => {
                        format!("JSON parsing error in file '{}': {}", bind_data.file_path, msg)
                    }
                    DataLoaderError::SchemaError(msg) => {
                        format!("Schema error in file '{}': {}", bind_data.file_path, msg)
                    }
                    DataLoaderError::CapacityExceeded { vector_path, required, available } => {
                        format!("Memory capacity exceeded for vector '{}': required {}, available {}", vector_path, required, available)
                    }
                    _ => {
                        format!("Error reading JSON file '{}': {:?}", bind_data.file_path, e)
                    }
                };

                output.set_len(0);
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished due to error");
                return Err(error_msg.into());
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}