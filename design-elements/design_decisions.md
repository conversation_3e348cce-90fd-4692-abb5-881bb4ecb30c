# Design Decisions

## Core Architecture

### Unified Recursive Streaming Architecture
- **Implementation**: Single recursive function `insert_json_value_recursive()` handles all JSON value types uniformly
- **Critical Success Factor**: Avoids special-case handling and enables arbitrary nesting depth without hardcoded limits
- **Vector Routing**: Routes to appropriate vector insertion logic based on value type and expected schema

### Pure Struson Streaming
- **Implementation**: Direct streaming from JSON to DuckDB vectors using only struson parser
- **Memory Efficiency**: Achieves O(row_size) instead of O(file_size) memory usage
- **Temporary Structures**: Use `TempValue` and `NestedTempValue` only for collection during streaming, not full parsing

### DuckDB Vector Management Patterns
- **Arrays of Objects**: Use `list_vector.struct_child(capacity)`
- **Struct Fields**: Use `struct_vector.child(field_idx, capacity)`
- **Multi-dimensional Arrays**: Recursive list vector management with proper capacity allocation
- **Critical**: Each array element must get its own memory location to avoid data duplication

### Schema Discovery Strategy
- **Implementation**: `discover_json_schema()` with recursive type analysis of first element
- **Rationale**: Sufficient for homogeneous data, enables schema-driven processing for type safety
- **Scope**: Handles arbitrary nesting depth without hardcoded limits

### Schema Validation: Strict vs Permissive Approach
- **Current Decision**: Strict schema validation - reject inconsistent data with clear error messages
- **Example**: Mixed-depth arrays like `[1, [2, 3], [[4, 5]]]` are rejected as schema conflicts
- **Rationale**:
  - Ensures data quality and type safety
  - Provides clear feedback about data structure issues
  - Prevents undefined behavior from inconsistent schemas
- **DuckDB Default Comparison**: DuckDB's default JSON reader is more permissive:
  - Converts mixed types to `JSON[]` with string representations
  - Example: `[1, [2, 3], [[4, 5]]]` → `[('1',), ('[2,3]',), ('[[4,5]]',)]`
- **Future Consideration**: May add permissive mode in future versions for better compatibility
- **Trade-offs**:
  - **Strict (Current)**: Better type safety, clearer errors, forces data quality
  - **Permissive (DuckDB)**: Better compatibility, handles edge cases, may hide data issues

## Critical Anti-Patterns to Avoid

### Never Manual Nesting Logic
- **Anti-Pattern**: Manually implementing N levels of nested logic (if depth == 1, if depth == 2, etc.)
- **Correct Approach**: Use recursive helper functions that handle arbitrary depth

### No Massive Functions with Duplicated Patterns
- **Anti-Pattern**: Single large function handling all cases with repeated code patterns
- **Correct Approach**: Break into focused helper functions with clear responsibilities

### Depth Limits are Anti-Patterns
- **Anti-Pattern**: Hardcoded depth limits (5, 10, 20 levels)
- **Correct Approach**: True recursive processing without artificial constraints

### VARCHAR Fallbacks Break Type System
- **Anti-Pattern**: Converting complex JSON types to VARCHAR when processing becomes difficult
- **Correct Approach**: Always use proper DuckDB types (STRUCT, LIST) with recursive vector handling

### Null Placeholders for Valid Data
- **Anti-Pattern**: Setting valid JSON structures to null when processing is complex
- **Correct Approach**: Preserve all valid JSON structures with complete data

## Implementation Patterns That Work

### Recursive Helper Functions
- `collect_nested_array_data_recursive()` - Multi-dimensional array collection
- `insert_nested_array_recursive()` - Multi-dimensional array insertion
- `insert_array_within_struct()` - Arrays within objects handling

### Temporary Value Types for Streaming
- `TempValue` - Basic primitive collection during streaming
- `NestedTempValue` - Recursive array support with `Array(Vec<NestedTempValue>)`

### Schema-Driven Processing
- Use discovered schema to guide vector allocation and type handling
- Ensures type safety and proper memory management

## Type Mapping
- JSON String → DuckDB VARCHAR
- JSON Number → DuckDB DOUBLE
- JSON Boolean → DuckDB BOOLEAN
- JSON Object → DuckDB STRUCT
- JSON Array → DuckDB LIST
- JSON null → DuckDB NULL

## API Design

### Table Function Interface
```sql
SELECT * FROM streaming_json_reader('path/to/file.json')
```

### Root-Level Array Flattening
- Arrays at root level are flattened into separate rows
- `[{"id": 1}, {"id": 2}]` becomes two rows with `id` column
- Matches DuckDB's `read_json_auto()` behavior for compatibility

### Error Handling
- Fail-fast on malformed JSON rather than attempting partial recovery
- Clear failure modes for debugging
